function getSettingsDefinitionsResponseBuilder() {
  // Mock YAML content for settings definitions
  const yamlContent = `
version: 1
orgId: 6c039d20-8a03-45de-ba5e-54b3ef581d85
productId: test-product-id
namespace: chat
settings:
  - settingName: voice
    valueType: boolean
    userHidden: false
    required: false
    autoReviewConsent: false
    label:
      en: Voice Chat
    parentNotice:
      en: Allow voice chat
    userNotice:
      en: Voice chat permission
    regions: []
    irrevocable: false
  - settingName: text
    valueType: boolean
    userHidden: false
    required: false
    autoReviewConsent: false
    label:
      en: Text Chat
    parentNotice:
      en: Allow text chat
    userNotice:
      en: Text chat permission
    regions: []
    irrevocable: true
---
version: 1
orgId: 6c039d20-8a03-45de-ba5e-54b3ef581d85
productId: test-product-id
namespace: default
settings:
  - settingName: geolocation
    valueType: boolean
    userHidden: false
    required: false
    autoReviewConsent: false
    label:
      en: Geolocation
    parentNotice:
      en: Allow geolocation tracking
    userNotice:
      en: Geolocation permission
    regions: []
    irrevocable: false
`;

  return {
    statusCode: 200,
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      response: {
        definitions: [yamlContent.trim()],
      },
    }),
  };
}

module.exports = getSettingsDefinitionsResponseBuilder;
