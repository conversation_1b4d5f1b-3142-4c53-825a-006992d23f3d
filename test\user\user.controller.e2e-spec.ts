import { INestApplication } from '@nestjs/common';
import { getDataSourceToken } from '@nestjs/typeorm';
import nock from 'nock';
import { DataSource, Repository } from 'typeorm';

import { App } from '../../src/app/app.entity';
import { Activation } from '../../src/user/activation.entity';
import { EbadgerDecision } from '../../src/user/badger.service';
import { UserRegisterDTO } from '../../src/user/user.dto';
import { User } from '../../src/user/user.entity';
import { createUserInDb, mockSettingsDefinitionsEndpoint, Utils } from '../utils';
import { OTHER_APP_NAME, TEST_APP_NAME } from '../utils/constants';
import { makeRequest } from '../utils/request-helper';

describe('UsersController (e2e)', () => {
  let rootApp: INestApplication;
  let JWT_OTHER_APP_TOKEN: string;
  let JWT_APP_TOKEN: string;
  let dataSource: DataSource;
  let testApp: App;
  let otherApp: App;
  let appRepo: Repository<App>;
  let userRepo: Repository<User>;

  beforeAll(async () => {
    rootApp = await Utils.createTestServer();
    dataSource = rootApp.get<DataSource>(getDataSourceToken());
    appRepo = dataSource.getRepository(App);
    userRepo = dataSource.getRepository(User);

    await Utils.cleanDb();
    const fixtures = await Utils.loadFixtures();
    testApp = fixtures.App.find((app) => app.name === TEST_APP_NAME) as App;
    otherApp = fixtures.App.find((app) => app.name === OTHER_APP_NAME) as App;

    Utils.mockAgeGateAPI();
    Utils.mockSettingsBackendAPI();
    Utils.mockAnalyticServiceAPI();
    Utils.mockGetUserFamily();
    Utils.mockPreverificationServiceAPI();
    Utils.mockDevPortalAPI();
    Utils.mockKeycloakTokenSettingResponse()

    JWT_APP_TOKEN = await Utils.getAppOAuthAppToken(testApp.id);
    JWT_OTHER_APP_TOKEN = await Utils.getAppOAuthAppToken(otherApp.id);
  });

  afterAll(async () => {
    await Utils.stopTestServer(rootApp);
    jest.clearAllMocks();
  });

  describe('GET /v1/users/:userId/apps', () => {
    let userId = -1;

    beforeAll(async () => {
      userId = await createUserInDb(testApp);
    });

    it('grants user activation', async () => {
      const app = await appRepo.findOneOrFail({ where: { id: otherApp.id } });
      const userToken = await Utils.getAppOAuthUserToken(app.id, userId);
      mockSettingsDefinitionsEndpoint()

      Utils.mockFamilyServiceGuardianRequest('<EMAIL>');
      await makeRequest(rootApp, 'post', `/v1/users/${userId}/apps`, {
        headers: {
          Authorization: `Bearer ${userToken}`,
          'x-forwarded-host': otherApp.orgEnv.host,
        },
        body: {
          appName: app.name,
          permissions: ['chat.voice'],
        },
        expectedStatus: 201,
      });

      const activation = await dataSource.getRepository(Activation).findOne({
        where: {
          user: { id: userId },
          app: { id: otherApp.id },
        },
      });
      expect(activation).not.toBeNull();
    });

    it('rejects user activation for other app with token not from same app', async () => {
      const app = await appRepo.findOneOrFail({ where: { id: testApp.id } });

      await makeRequest(rootApp, 'post', `/v1/users/${userId}/apps`, {
        headers: {
          Authorization: `Bearer ${JWT_OTHER_APP_TOKEN}`,
          'x-forwarded-host': testApp.orgEnv.host,
        },
        body: {
          appName: app.name,
          permissions: ['chat.voice'],
        },
        expectedStatus: 403,
      });
    });
  });

  describe('GET /v1/users/check-username', () => {
    let mockBadgerBaseUrl: string;
    beforeEach(() => {
      nock.cleanAll();
      mockBadgerBaseUrl = Utils.configService.getBadgerConfig().baseURL;
    });

    const mockBadgerResponse = (decision: EbadgerDecision = EbadgerDecision.ALLOW): nock.Scope => {
      return nock(mockBadgerBaseUrl).post('/v4/moderate/screen/kws_usernames').reply(200, {
        action: decision,
        tags: [],
      });
    };

    const checkUsername = async (
      username: string,
      expectedStatus = 200,
      additionalHeaders: Record<string, string> = {},
    ) => {
      return makeRequest(rootApp, 'get', `/v1/users/check-username`, {
        headers: {
          'x-forwarded-host': testApp.orgEnv.host,
          ...additionalHeaders,
        },
        query: { username },
        expectedStatus,
      });
    };

    it('should return available true when username is available and passes moderation', async () => {
      const username = 'validUsername';
      mockBadgerResponse(EbadgerDecision.ALLOW);

      const response = await checkUsername(username);

      expect(response.body).toEqual({
        username,
        available: true,
        details: {
          isValid: true,
          isAvailable: true,
        },
      });
    });

    it('should return available false when username is already taken', async () => {
      const takenUsername = 'takenUsername';

      const user = userRepo.create({
        username: takenUsername,
        orgEnvId: testApp.orgEnv.id,
      });
      await userRepo.save(user);

      mockBadgerResponse(EbadgerDecision.ALLOW);

      const response = await checkUsername(takenUsername);

      expect(response.body).toEqual({
        username: takenUsername,
        available: false,
        details: {
          isValid: true,
          isAvailable: false,
          reasons: ['Username is already taken.'],
        },
      });
    });

    it('should return available false when username fails moderation', async () => {
      const username = 'inappropriateUsername';
      mockBadgerResponse(EbadgerDecision.REJECT);

      const response = await checkUsername(username);

      expect(response.body).toEqual({
        username,
        available: false,
        details: {
          isValid: false,
          isAvailable: true,
          reasons: [`Username moderation judgement is '${EbadgerDecision.REJECT}'.`],
        },
      });
    });

    it('should handle language parameter correctly', async () => {
      const username = 'validUsername';
      const language = 'fr-FR';

      const badgerScope = mockBadgerResponse(EbadgerDecision.ALLOW);

      await checkUsername(username, 200, { 'accept-language': language });

      expect(badgerScope.isDone()).toBe(true);
    });
  });

  describe('POST /v2/users', () => {
    beforeAll(() => {
      const talonBaseUrl = 'https://talon-service-prod.ol.epicgames.com';
      nock(talonBaseUrl).persist().post('/v1/verify').reply(200, { solved: true });

      const badgerBaseUrl = 'https://moderation-ingest-service-prod-external.ecbc.live.use1a.on.epicgames.com';
      nock(badgerBaseUrl)
        .persist()
        .post('/v4/moderate/screen/kws_usernames')
        .reply(200, { decision: EbadgerDecision.ALLOW });

      Utils.mockAgeGateAPI();
    });

    it('should register a new user successfully', async () => {
      const registerDto: UserRegisterDTO = {
        username: 'testuser123',
        password: 'Password123!',
        dateOfBirth: '2020-01-01',
        token: 'valid-token',
        originAppId: testApp.id,
        language: 'en',
      };

      const response = await makeRequest(rootApp, 'post', '/v2/users', {
        headers: {
          Authorization: `Bearer ${JWT_APP_TOKEN}`,
          'x-forwarded-host': testApp.orgEnv.host,
        },
        body: registerDto,
        expectedStatus: 201,
      });

      expect(response.body).toHaveProperty('id');

      const createdUser = await userRepo.findOne({
        where: { username: registerDto.username },
      });

      expect(createdUser).toBeDefined();
      expect(createdUser?.username).toBe(registerDto.username);
      expect(createdUser?.language).toBe(registerDto.language);
      expect(createdUser?.orgEnvId).toBe(testApp.orgEnv.id);
    });

    it('should reject registration when username and password are both missing', async () => {
      const invalidDto = {
        dateOfBirth: '2020-01-01',
        token: 'valid-token',
        originAppId: testApp.id,
        language: 'en',
      } as unknown as UserRegisterDTO;

      await makeRequest(rootApp, 'post', '/v2/users', {
        headers: {
          Authorization: `Bearer ${JWT_APP_TOKEN}`,
          'x-forwarded-host': testApp.orgEnv.host,
        },
        body: invalidDto,
        expectedStatus: 400,
      });
    });

    it('should reject registration when username is already taken', async () => {
      // Save a conflicting user with the same username
      await userRepo.save({
        username: 'existinguser',
        orgEnvId: testApp.orgEnv.id,
        language: 'en',
        dateOfBirth: new Date('2010-01-01'),
      });

      const registerDto: UserRegisterDTO = {
        username: 'existinguser',
        password: 'Password123!',
        dateOfBirth: '2010-01-01',
        token: 'valid-token',
        originAppId: testApp.id,
        language: 'en',
      };

      const response = await makeRequest(rootApp, 'post', '/v2/users', {
        headers: {
          Authorization: `Bearer ${JWT_APP_TOKEN}`,
          'x-forwarded-host': testApp.orgEnv.host,
        },
        body: registerDto,
        expectedStatus: 409,
      });

      expect(response.body.errorMessage).toBe('Username is already taken');
    });
  });
});
